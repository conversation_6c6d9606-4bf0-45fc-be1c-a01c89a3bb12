import { apiCall } from '@/lib/api';
import { optionFetchSearchAPI } from '@/networks/search/api';

// Types based on the backend APIs provided
export interface CheckUsernameQueryI {
  username: string;
}

export interface UpdateUsernameBodyI {
  username: string;
}

export interface UpdateUsernameResultI {
  profileId: string;
  username: string;
  isUsernameSaved: boolean;
}

export interface OnboardingPersonalBodyI {
  fullName?: string;
  countryIso2?: string;
  gender?: string | null;
}

export interface OnboardingPersonalResultI {
  isPersonalDetailsSaved: boolean;
  profileId: string;
}

export interface OnboardingWorkBodyI {
  designation: {
    id: string;
    dataType: string;
  };
  entity?: {
    id: string;
    dataType: string;
  };
}

export interface OnboardingWorkResultI {
  isWorkDetailsSaved: boolean;
  profileId: string;
}

export interface SearchResultI {
  id: string;
  name: string;
  dataType: string;
}

export const checkUsernameAPI = async (query: CheckUsernameQueryI): Promise<{ available: boolean }> => {
  const result = await apiCall<CheckUsernameQueryI, { available: boolean }>('/backend/api/v1/user/profile/username', 'GET', {
    isAuth: true,
    query,
  });

  return result;
};

export const updateUsernameAPI = async (
  payload: UpdateUsernameBodyI,
): Promise<UpdateUsernameResultI> => {
  const result = await apiCall<UpdateUsernameBodyI, UpdateUsernameResultI>(
    '/backend/api/v1/user/profile/username',
    'PATCH',
    { isAuth: true, payload },
  );

  return result;
};

export const onboardingPersonalAPI = async (
  payload: OnboardingPersonalBodyI,
): Promise<OnboardingPersonalResultI> => {
  const result = await apiCall<OnboardingPersonalBodyI, OnboardingPersonalResultI>(
    '/backend/api/v1/user/profile/onboarding/personal',
    'POST',
    { isAuth: true, payload },
  );

  return result;
};

export const onboardingWorkAPI = async (
  payload: OnboardingWorkBodyI,
): Promise<OnboardingWorkResultI> => {
  const result = await apiCall<OnboardingWorkBodyI, OnboardingWorkResultI>(
    '/backend/api/v1/user/profile/onboarding/work',
    'POST',
    { isAuth: true, payload },
  );

  return result;
};

// Search functions using the existing optionFetchSearchAPI
export const searchDesignationsAPI = async (query: string): Promise<SearchResultI[]> => {
  try {
    const result = await optionFetchSearchAPI('designation', query, '1');
    return result.data.map(item => ({
      id: item.id,
      name: item.name,
      dataType: item.dataType,
    }));
  } catch (error) {
    console.error('Error searching designations:', error);
    return [];
  }
};

export const searchEntitiesAPI = async (query: string): Promise<SearchResultI[]> => {
  try {
    const result = await optionFetchSearchAPI('entity', query, '1');
    return result.data.map(item => ({
      id: item.id,
      name: item.name,
      dataType: item.dataType,
    }));
  } catch (error) {
    console.error('Error searching entities:', error);
    return [];
  }
};

export const getCountriesAPI = async (): Promise<{ value: string; label: string }[]> => {
  try {
    const result = await optionFetchSearchAPI('country', '1', undefined);
    return result.data.map(item => ({
      value: item.id,
      label: item.name,
    }));
  } catch (error) {
    console.error('Error fetching countries:', error);
    // Fallback countries
    return [
      { value: 'US', label: 'United States' },
      { value: 'IN', label: 'India' },
      { value: 'GB', label: 'United Kingdom' },
      { value: 'CA', label: 'Canada' },
      { value: 'AU', label: 'Australia' },
    ];
  }
};
