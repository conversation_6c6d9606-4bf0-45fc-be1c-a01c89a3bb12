'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Control, Controller } from 'react-hook-form';
import { ChevronDownIcon, CheckIcon } from '@heroicons/react/24/outline';
import { optionFetchSearchAPI } from '@/networks/search/api';

type CountryOption = {
  value: string;
  label: string;
};

type CountrySelectProps = {
  control: Control<any>;
  name?: string;
  isRequired?: boolean;
  disabled?: boolean;
  label?: string;
  placeholder?: string;
  className?: string;
};

const CountrySelect = ({
  control,
  name = 'countryIso2',
  isRequired = false,
  disabled = false,
  label = 'Country',
  placeholder = 'Search and select your country',
  className = 'w-full'
}: CountrySelectProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [countries, setCountries] = useState<CountryOption[]>([]);
  const [filteredCountries, setFilteredCountries] = useState<CountryOption[]>([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [initialLoad, setInitialLoad] = useState(false);

  const dropdownRef = useRef<HTMLDivElement>(null);
  const listRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Load countries from API
  const loadCountries = useCallback(async (searchText = '', pageNum = 1, reset = false) => {
    if (loading) return;

    setLoading(true);
    try {
      const result = await optionFetchSearchAPI('country', searchText, pageNum.toString());
      const newCountries = result.data.map(item => ({
        value: item.id,
        label: item.name,
      }));

      if (reset || pageNum === 1) {
        setCountries(newCountries);
        setFilteredCountries(newCountries);
      } else {
        setCountries(prev => [...prev, ...newCountries]);
        setFilteredCountries(prev => [...prev, ...newCountries]);
      }

      setHasMore(newCountries.length > 0 && result.total > countries.length + newCountries.length);
      setPage(pageNum);
    } catch (error) {
      console.error('Error loading countries:', error);
      // Fallback countries
      if (pageNum === 1) {
        const fallbackCountries = [
          { value: 'US', label: 'United States' },
          { value: 'IN', label: 'India' },
          { value: 'GB', label: 'United Kingdom' },
          { value: 'CA', label: 'Canada' },
          { value: 'AU', label: 'Australia' },
          { value: 'DE', label: 'Germany' },
          { value: 'FR', label: 'France' },
          { value: 'JP', label: 'Japan' },
          { value: 'BR', label: 'Brazil' },
          { value: 'CN', label: 'China' },
        ];
        setCountries(fallbackCountries);
        setFilteredCountries(fallbackCountries);
        setHasMore(false);
      }
    } finally {
      setLoading(false);
      setInitialLoad(true);
    }
  }, [loading, countries.length]);

  // Initial load
  useEffect(() => {
    if (!initialLoad) {
      loadCountries('', 1, true);
    }
  }, [loadCountries, initialLoad]);

  // Search functionality
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchQuery.trim()) {
        loadCountries(searchQuery, 1, true);
      } else if (initialLoad) {
        loadCountries('', 1, true);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchQuery, loadCountries, initialLoad]);

  // Handle scroll for pagination
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    if (scrollHeight - scrollTop <= clientHeight + 5 && hasMore && !loading) {
      loadCountries(searchQuery, page + 1, false);
    }
  }, [hasMore, loading, searchQuery, page, loadCountries]);

  // Click outside to close
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);

  return (
    <Controller
      control={control}
      name={name}
      rules={{
        required: isRequired ? 'Please select your country' : false,
      }}
      render={({ field: { onChange, value }, fieldState: { error } }) => {
        const selectedCountry = countries.find(country => country.value === value);

        const handleSelect = (country: CountryOption) => {
          onChange(country.value);
          setIsOpen(false);
          setSearchQuery('');
        };

        return (
          <div className={`relative ${className}`}>
            {label && (
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {label}
              </label>
            )}
            <div className="relative" ref={dropdownRef}>
              <button
                type="button"
                className={`
                  relative w-full cursor-default rounded-md border bg-white py-2 pl-3 pr-10 text-left shadow-sm
                  focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary sm:text-sm
                  ${error ? 'border-red-300' : 'border-gray-300'}
                  ${disabled ? 'bg-gray-50 text-gray-500 cursor-not-allowed' : 'hover:border-gray-400'}
                `}
                aria-haspopup="listbox"
                aria-expanded={isOpen}
                onClick={() => !disabled && setIsOpen(!isOpen)}
                disabled={disabled}
              >
                <span className="block truncate">
                  {selectedCountry ? selectedCountry.label : placeholder}
                </span>
                <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                  <ChevronDownIcon
                    className={`h-5 w-5 text-gray-400 transition-transform duration-200 ${
                      isOpen ? 'rotate-180' : ''
                    }`}
                    aria-hidden="true"
                  />
                </span>
              </button>

              {isOpen && (
                <div className="absolute z-10 mt-1 w-full rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                  {/* Search Input */}
                  <div className="px-3 py-2 border-b border-gray-200">
                    <input
                      ref={searchInputRef}
                      type="text"
                      className="w-full px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                      placeholder="Search countries..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>

                  {/* Countries List */}
                  <div
                    ref={listRef}
                    className="max-h-60 overflow-auto"
                    onScroll={handleScroll}
                  >
                    {filteredCountries.map((country) => (
                      <div
                        key={country.value}
                        className={`
                          relative cursor-pointer select-none py-2 pl-3 pr-9
                          text-gray-900 hover:bg-primary hover:text-white
                          ${value === country.value ? 'bg-primary text-white' : ''}
                        `}
                        onClick={() => handleSelect(country)}
                      >
                        <span className={`block truncate ${value === country.value ? 'font-medium' : 'font-normal'}`}>
                          {country.label}
                        </span>
                        {value === country.value && (
                          <span className="absolute inset-y-0 right-0 flex items-center pr-4">
                            <CheckIcon className="h-5 w-5" aria-hidden="true" />
                          </span>
                        )}
                      </div>
                    ))}

                    {loading && (
                      <div className="px-3 py-2 text-center text-gray-500">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mx-auto"></div>
                        <span className="text-sm mt-1">Loading...</span>
                      </div>
                    )}

                    {!loading && filteredCountries.length === 0 && searchQuery && (
                      <div className="px-3 py-2 text-center text-gray-500 text-sm">
                        No countries found
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
            {error && (
              <p className="mt-1 text-sm text-red-600">{error.message}</p>
            )}
          </div>
        );
      }}
    />
  );
};

export default CountrySelect;