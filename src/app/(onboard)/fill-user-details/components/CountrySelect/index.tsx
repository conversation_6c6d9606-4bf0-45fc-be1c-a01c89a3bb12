import { Select } from '@/components'
import React from 'react'
import { Control, Controller } from 'react-hook-form'

const CountrySelect = ({ control, isRequired = false, disabled = false }: { control: Control<{ countryIso2: string }>, isRequired?: boolean, disabled?: boolean }) => {
    return (
        <Controller
            control={control}
            name={"countryIso2"}
            rules={{
                required: isRequired ? 'Please select your country' : false,
            }}
            render={({ field: { onChange, value }, fieldState: { error } }) => (
                <Select
                    label="Country"
                    placeholder={
                        loadingCountries
                            ? 'Loading countries...'
                            : 'Select your country'
                    }
                    options={countryOptions}
                    value={value}
                    onValueChange={onChange}
                    error={error?.message}
                    disabled={disabled ?? false}
                    className="w-full"
                    searchable
                />
            )}
        />
    )
}

export default CountrySelect