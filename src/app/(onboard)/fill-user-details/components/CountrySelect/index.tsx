'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Control, Controller } from 'react-hook-form';
import { Select } from '@/components';
import type { SelectOptionI } from '@/components/Select';
import { optionFetchSearchAPI } from '@/networks/search/api';

type CountrySelectProps = {
    control: Control<any>;
    name?: string;
    isRequired?: boolean;
    disabled?: boolean;
    label?: string;
    placeholder?: string;
    className?: string;
};

const CountrySelect = ({
    control,
    name = 'countryIso2',
    isRequired = false,
    disabled = false,
    label = 'Country',
    placeholder = 'Select your country',
    className = 'w-full'
}: CountrySelectProps) => {
    const [countries, setCountries] = useState<SelectOptionI[]>([]);
    const [loading, setLoading] = useState(false);
    const [page, setPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);
    const [currentSearchQuery, setCurrentSearchQuery] = useState('');

    const loadCountries = useCallback(async (searchText = '', pageNum = 1, reset = false) => {
        if (loading) return;

        setLoading(true);
        try {
            const result = await optionFetchSearchAPI('country', pageNum.toString(), (searchText || ""));
            const newCountries = result.data.map(item => ({
                value: item.id,
                label: item.name,
            }));

            if (reset || pageNum === 1) {
                setCountries(newCountries);
            } else {
                setCountries(prev => [...prev, ...newCountries]);
            }
            setHasMore(newCountries.length > 0 && result.total > countries.length + newCountries.length);
            setPage(pageNum);
        } catch (error) {
            console.error('Error loading countries:', error);

            if (pageNum === 1) {
                // const fallbackCountries = [
                //   { value: 'US', label: 'United States' },
                //   { value: 'IN', label: 'India' },
                //   { value: 'GB', label: 'United Kingdom' },
                //   { value: 'CA', label: 'Canada' },
                //   { value: 'AU', label: 'Australia' },
                //   { value: 'DE', label: 'Germany' },
                //   { value: 'FR', label: 'France' },
                //   { value: 'JP', label: 'Japan' },
                //   { value: 'BR', label: 'Brazil' },
                //   { value: 'CN', label: 'China' },
                // ];
                // setCountries(fallbackCountries);
                setHasMore(false);
            }
        } finally {
            setLoading(false);
        }
    }, [loading]);

    // Initial load
    useEffect(() => {
        loadCountries('', 1, true);
    }, []);

    // Handle search
    const handleSearch = useCallback((query: string) => {
        setCurrentSearchQuery(query);
        setPage(1);
        // loadCountries(query, 1, true);
    }, [loadCountries]);

    // Handle load more
    const handleLoadMore = useCallback(() => {
        if (hasMore && !loading) {
            loadCountries(currentSearchQuery, page + 1, false);
        }
    }, [hasMore, loading, currentSearchQuery, page, loadCountries]);

    return (
        <Controller
            control={control}
            name={name}
            rules={{
                required: isRequired ? 'Please select your country' : false,
            }}
            render={({ field: { onChange, value }, fieldState: { error } }) => (
                <Select
                    label={label}
                    placeholder={placeholder}
                    options={countries ?? []}
                    value={value}
                    onValueChange={onChange}
                    error={error?.message}
                    disabled={disabled}
                    className={className}
                    searchable={true}
                    onSearch={handleSearch}
                    onLoadMore={handleLoadMore}
                    hasMore={hasMore}
                    loading={loading}
                    searchPlaceholder="Search countries..."
                />
            )}
        />
    );
};

export default CountrySelect;