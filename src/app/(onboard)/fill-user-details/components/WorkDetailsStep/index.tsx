'use client';

import { useForm, Controller } from 'react-hook-form';
import { Button, SearchableSelect } from '@/components';
import { useRouter } from 'next/navigation';
import type { SearchableOptionI } from '@/components/SearchableSelect';

type WorkDetailsFormData = {
  designation: SearchableOptionI | null;
  entity: SearchableOptionI | null;
};

const WorkDetailsStep = () => {
  const router = useRouter();

  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    setError,
  } = useForm<WorkDetailsFormData>({
    defaultValues: {
      designation: null,
      entity: null,
    },
  });

  const searchDesignations = async (query: string): Promise<SearchableOptionI[]> => {
    try {
      const response = await fetch('/api/search-designations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query }),
      });
      const data = await response.json();
      return data.results || [];
    } catch (error) {
      console.error('Error searching designations:', error);
      return [];
    }
  };

  const searchEntities = async (query: string): Promise<SearchableOptionI[]> => {
    try {
      const response = await fetch('/api/search-entities', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query }),
      });
      const data = await response.json();
      return data.results || [];
    } catch (error) {
      console.error('Error searching entities:', error);
      return [];
    }
  };

  const onSubmit = async (data: WorkDetailsFormData) => {
    try {
      if (!data.designation || !data.entity) {
        throw new Error('Please select both designation and organization');
      }

      const payload = {
        designation: {
          id: data.designation.id,
          dataType: data.designation.dataType,
        },
        entity: {
          id: data.entity.id,
          dataType: data.entity.dataType,
        },
      };

      const response = await fetch('/api/save-work-details', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to save work details');
      }

      // Navigate to main app after completion
      router.push('/forums');
    } catch (error) {
      console.error('Error saving work details:', error);
      setError('designation', {
        type: 'manual',
        message: error instanceof Error ? error.message : 'Failed to save work details. Please try again.',
      });
    }
  };

  const handleBack = () => {
    router.push('/fill-user-details?step=personal');
  };

  const handleSkip = () => {
    router.push('/forums');
  };

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-3xl font-semibold text-black mb-2">
          Work Details
        </h1>
        <p className="text-gray-600">
          Share your professional background and current role.
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <Controller
          control={control}
          name="designation"
          rules={{
            required: 'Please select a designation',
          }}
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <SearchableSelect
              label="Designation"
              placeholder="Search for your designation..."
              value={value}
              onValueChange={onChange}
              onSearch={searchDesignations}
              error={error?.message}
              disabled={isSubmitting}
              className="w-full"
            />
          )}
        />

        <Controller
          control={control}
          name="entity"
          rules={{
            required: 'Please select an organization',
          }}
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <SearchableSelect
              label="Organization"
              placeholder="Search for your organization..."
              value={value}
              onValueChange={onChange}
              onSearch={searchEntities}
              error={error?.message}
              disabled={isSubmitting}
              className="w-full"
            />
          )}
        />

        <div className="flex gap-3 pt-4">
          <Button
            type="button"
            onClick={handleBack}
            className="flex-1 rounded-full border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 text-base font-semibold py-3"
            disabled={isSubmitting}
          >
            Back
          </Button>
          <Button
            type="submit"
            className="flex-1 rounded-full bg-[#448600] hover:bg-[#357000] text-white text-base font-semibold py-3"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Completing...' : 'Complete'}
          </Button>
        </div>

        <div className="text-center pt-2">
          <button
            type="button"
            onClick={handleSkip}
            className="text-gray-600 hover:text-gray-800 text-sm font-medium underline"
            disabled={isSubmitting}
          >
            Skip for now
          </button>
        </div>
      </form>
    </div>
  );
};

export default WorkDetailsStep;
