'use client';

import { useForm, Controller } from 'react-hook-form';
import { Button, Input } from '@/components';
import { useRouter } from 'next/navigation';

type WorkDetailsFormData = {
  designation: string;
  organisation: string;
};

const WorkDetailsStep = () => {
  const router = useRouter();

  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    setError,
  } = useForm<WorkDetailsFormData>({
    defaultValues: {
      designation: '',
      organisation: '',
    },
  });

  const onSubmit = async (data: WorkDetailsFormData) => {
    try {
      const response = await fetch('/api/save-work-details', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to save work details');
      }

      // Navigate to main app after completion
      router.push('/forums');
    } catch (error) {
      console.error('Error saving work details:', error);
      setError('designation', {
        type: 'manual',
        message: error instanceof Error ? error.message : 'Failed to save work details. Please try again.',
      });
    }
  };

  const handleBack = () => {
    router.push('/fill-user-details?step=personal');
  };

  const handleSkip = () => {
    router.push('/forums');
  };

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-3xl font-semibold text-black mb-2">
          Work Details
        </h1>
        <p className="text-gray-600">
          Share your professional background and current role.
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <Controller
          control={control}
          name="designation"
          rules={{
            required: 'Designation is required',
            minLength: {
              value: 2,
              message: 'Designation must be at least 2 characters',
            },
            maxLength: {
              value: 100,
              message: 'Designation cannot exceed 100 characters',
            },
          }}
          render={({ field: { onChange, value, onBlur }, fieldState: { error } }) => (
            <Input
              label="Designation"
              placeholder="e.g. Software Engineer, Product Manager"
              value={value}
              onChange={(e) => onChange(e.target.value)}
              onBlur={onBlur}
              error={error?.message}
              disabled={isSubmitting}
              className="w-full"
            />
          )}
        />

        <Controller
          control={control}
          name="organisation"
          rules={{
            required: 'Organisation is required',
            minLength: {
              value: 2,
              message: 'Organisation must be at least 2 characters',
            },
            maxLength: {
              value: 100,
              message: 'Organisation cannot exceed 100 characters',
            },
          }}
          render={({ field: { onChange, value, onBlur }, fieldState: { error } }) => (
            <Input
              label="Organisation"
              placeholder="e.g. Google, Microsoft, Freelancer"
              value={value}
              onChange={(e) => onChange(e.target.value)}
              onBlur={onBlur}
              error={error?.message}
              disabled={isSubmitting}
              className="w-full"
            />
          )}
        />

        <div className="flex gap-3 pt-4">
          <Button
            type="button"
            onClick={handleBack}
            className="flex-1 rounded-full border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 text-base font-semibold py-3"
            disabled={isSubmitting}
          >
            Back
          </Button>
          <Button
            type="submit"
            className="flex-1 rounded-full bg-[#448600] hover:bg-[#357000] text-white text-base font-semibold py-3"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Completing...' : 'Complete'}
          </Button>
        </div>

        <div className="text-center pt-2">
          <button
            type="button"
            onClick={handleSkip}
            className="text-gray-600 hover:text-gray-800 text-sm font-medium underline"
            disabled={isSubmitting}
          >
            Skip for now
          </button>
        </div>
      </form>
    </div>
  );
};

export default WorkDetailsStep;
