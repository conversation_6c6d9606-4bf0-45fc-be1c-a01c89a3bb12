'use client';

import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { Button, Input, Select } from '@/components';
import { useRouter } from 'next/navigation';

type PersonalDetailsFormData = {
  fullName: string;
  gender: string;
  country: string;
};

const genderOptions = [
  { value: 'male', label: 'Male' },
  { value: 'female', label: 'Female' },
  { value: 'other', label: 'Other' },
  { value: 'prefer_not_to_say', label: 'Prefer not to say' },
];

const countryOptions = [
  { value: 'us', label: 'United States' },
  { value: 'ca', label: 'Canada' },
  { value: 'uk', label: 'United Kingdom' },
  { value: 'au', label: 'Australia' },
  { value: 'de', label: 'Germany' },
  { value: 'fr', label: 'France' },
  { value: 'in', label: 'India' },
  { value: 'jp', label: 'Japan' },
  { value: 'br', label: 'Brazil' },
  { value: 'mx', label: 'Mexico' },
  { value: 'es', label: 'Spain' },
  { value: 'it', label: 'Italy' },
  { value: 'nl', label: 'Netherlands' },
  { value: 'se', label: 'Sweden' },
  { value: 'no', label: 'Norway' },
  { value: 'dk', label: 'Denmark' },
  { value: 'fi', label: 'Finland' },
  { value: 'ch', label: 'Switzerland' },
  { value: 'at', label: 'Austria' },
  { value: 'be', label: 'Belgium' },
  { value: 'ie', label: 'Ireland' },
  { value: 'nz', label: 'New Zealand' },
  { value: 'sg', label: 'Singapore' },
  { value: 'hk', label: 'Hong Kong' },
  { value: 'kr', label: 'South Korea' },
  { value: 'cn', label: 'China' },
  { value: 'ae', label: 'United Arab Emirates' },
  { value: 'sa', label: 'Saudi Arabia' },
  { value: 'za', label: 'South Africa' },
  { value: 'other', label: 'Other' },
];

const PersonalDetailsStep = () => {
  const router = useRouter();

  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    setError,
  } = useForm<PersonalDetailsFormData>({
    defaultValues: {
      fullName: '',
      gender: '',
      country: '',
    },
  });

  const onSubmit = async (data: PersonalDetailsFormData) => {
    try {
      const response = await fetch('/api/save-personal-details', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to save personal details');
      }

      // Navigate to work details step
      router.push('/fill-user-details?step=work');
    } catch (error) {
      console.error('Error saving personal details:', error);
      setError('fullName', {
        type: 'manual',
        message: error instanceof Error ? error.message : 'Failed to save personal details. Please try again.',
      });
    }
  };

  const handleBack = () => {
    router.push('/fill-user-details?step=username');
  };

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-3xl font-semibold text-black mb-2">
          Personal Details
        </h1>
        <p className="text-gray-600">
          Add your personal information to help others connect with you.
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <Controller
          control={control}
          name="fullName"
          rules={{
            required: 'Full name is required',
            minLength: {
              value: 2,
              message: 'Full name must be at least 2 characters',
            },
            maxLength: {
              value: 50,
              message: 'Full name cannot exceed 50 characters',
            },
          }}
          render={({ field: { onChange, value, onBlur }, fieldState: { error } }) => (
            <Input
              label="Full Name"
              placeholder="Enter your full name"
              value={value}
              onChange={(e) => onChange(e.target.value)}
              onBlur={onBlur}
              error={error?.message}
              disabled={isSubmitting}
              className="w-full"
            />
          )}
        />

        <Controller
          control={control}
          name="gender"
          rules={{
            required: 'Please select your gender',
          }}
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <Select
              label="Gender"
              placeholder="Select your gender"
              options={genderOptions}
              value={value}
              onValueChange={onChange}
              error={error?.message}
              disabled={isSubmitting}
              className="w-full"
            />
          )}
        />

        <Controller
          control={control}
          name="country"
          rules={{
            required: 'Please select your country',
          }}
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <Select
              label="Country"
              placeholder="Select your country"
              options={countryOptions}
              value={value}
              onValueChange={onChange}
              error={error?.message}
              disabled={isSubmitting}
              className="w-full"
            />
          )}
        />

        <div className="flex gap-3 pt-4">
          <Button
            type="button"
            onClick={handleBack}
            className="flex-1 rounded-full border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 text-base font-semibold py-3"
            disabled={isSubmitting}
          >
            Back
          </Button>
          <Button
            type="submit"
            className="flex-1 rounded-full bg-[#448600] hover:bg-[#357000] text-white text-base font-semibold py-3"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Saving...' : 'Continue'}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default PersonalDetailsStep;
