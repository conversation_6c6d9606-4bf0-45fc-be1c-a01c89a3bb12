'use client';

import { useState, useEffect } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { Button, Input } from '@/components';
import { useRouter } from 'next/navigation';

type UsernameFormData = {
  userName: string;
};

const UsernameStep = () => {
  const router = useRouter();
  const [isCheckingUsername, setIsCheckingUsername] = useState(false);
  const [usernameAvailable, setUsernameAvailable] = useState<boolean | null>(null);

  const {
    control,
    handleSubmit,
    watch,
    formState: { errors, isSubmitting },
    setError,
    clearErrors,
  } = useForm<UsernameFormData>({
    defaultValues: {
      userName: '',
    },
  });

  const watchedUsername = watch('userName');

  // Debounced username availability check
  useEffect(() => {
    if (!watchedUsername || watchedUsername.length < 4) {
      setUsernameAvailable(null);
      return;
    }

    const timeoutId = setTimeout(async () => {
      setIsCheckingUsername(true);
      try {
        // TODO: Replace with actual API call
        const response = await fetch(`/api/check-username?username=${watchedUsername}`);
        const data = await response.json();
        setUsernameAvailable(data.available);
        
        if (!data.available) {
          setError('userName', {
            type: 'manual',
            message: 'Username is already taken',
          });
        } else {
          clearErrors('userName');
        }
      } catch (error) {
        console.error('Error checking username:', error);
        setUsernameAvailable(null);
      } finally {
        setIsCheckingUsername(false);
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [watchedUsername, setError, clearErrors]);

  const validateUsername = (value: string) => {
    if (!value) return 'Username is required';
    if (value.length < 4) return 'Username must be at least 4 characters';
    if (value.length > 25) return 'Username cannot exceed 25 characters';
    if (/\s/.test(value)) return 'Username cannot contain spaces';
    if (/^[_]/.test(value)) return 'Cannot start with _';
    if (/^[.]/.test(value)) return 'Cannot start with .';
    if (/[.]$/.test(value)) return 'Cannot end with .';
    if (/[_]$/.test(value)) return 'Cannot end with _';
    if (/[_.]{2}/.test(value)) return 'Cannot contain consecutive periods/underscores';
    if (/^[._]+$/.test(value)) return 'Cannot contain only periods or underscores';
    if (!/^[a-zA-Z0-9._]+$/.test(value)) return 'Invalid username format';
    return true;
  };

  const onSubmit = async (data: UsernameFormData) => {
    try {
      const response = await fetch('/api/save-username', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username: data.userName }),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to save username');
      }

      // Navigate to personal details step
      router.push('/fill-user-details?step=personal');
    } catch (error) {
      console.error('Error saving username:', error);
      setError('userName', {
        type: 'manual',
        message: error instanceof Error ? error.message : 'Failed to save username. Please try again.',
      });
    }
  };

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-3xl font-semibold text-black mb-2">
          Choose Your Username
        </h1>
        <p className="text-gray-600">
          Choose a unique username that represents you professionally.
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <Controller
          control={control}
          name="userName"
          rules={{
            required: 'Username is required',
            validate: validateUsername,
          }}
          render={({ field: { onChange, value, onBlur }, fieldState: { error } }) => (
            <div>
              <Input
                label="Username"
                placeholder="Enter username (min 4 characters)"
                value={value}
                onChange={(e) => onChange(e.target.value)}
                onBlur={onBlur}
                error={error?.message}
                disabled={isSubmitting}
                className="w-full"
              />
              {isCheckingUsername && (
                <div className="mt-2 flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-[#448600]"></div>
                  <span className="text-sm text-gray-600">
                    Checking availability...
                  </span>
                </div>
              )}
              {usernameAvailable === true && !error && (
                <div className="mt-2 flex items-center gap-2">
                  <svg className="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span className="text-sm text-green-600">
                    Username is available!
                  </span>
                </div>
              )}
            </div>
          )}
        />

        <div className="pt-4">
          <Button
            type="submit"
            className="w-full rounded-full bg-[#448600] hover:bg-[#357000] text-white text-base font-semibold py-3"
            disabled={isSubmitting || isCheckingUsername || usernameAvailable === false}
          >
            {isSubmitting ? 'Saving...' : 'Continue'}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default UsernameStep;
