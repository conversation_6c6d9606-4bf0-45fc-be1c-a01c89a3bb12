'use client';

import Link from 'next/link';
import ForgotPasswordForm from './components/ForgotPasswordForm';

const ForgotPassword = () => {
    return (
        <div className="min-h-screen bg-gray-50 flex flex-col">
            <div className="flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:px-8">
                <div className="mx-auto w-full max-w-md">
                    <div className="bg-white shadow-lg rounded-lg p-8">
                        <div className="text-center mb-6">
                            <h1 className="text-2xl font-semibold text-gray-900 mb-4">
                                Forgot password
                            </h1>
                        </div>

                        {/* <ForgotPasswordForm /> */}
                    </div>
                </div>
                <div className="text-center mt-6 space-y-2">
                    <p className="text-sm text-gray-600">
                        Remembered your password?{' '}
                        <Link
                            href="/login"
                            className="text-primary font-semibold hover:underline"
                        >
                            Sign in
                        </Link>
                    </p>
                </div>
            </div>
        </div>
    );
};

export default ForgotPassword;
