'use client';

import { useSearchParams } from 'next/navigation';
import { useState, useEffect } from 'react';
import UsernameStep from './components/UsernameStep';
import PersonalDetailsStep from './components/PersonalDetailsStep';
import WorkDetailsStep from './components/WorkDetailsStep';

function FillUserDetailsPage() {
  const searchParams = useSearchParams();
  const step = searchParams.get('step') || 'username';
  const [currentStep, setCurrentStep] = useState(step);

  useEffect(() => {
    setCurrentStep(step);
  }, [step]);

  const renderStep = () => {
    switch (currentStep) {
      case 'username':
        return <UsernameStep />;
      case 'personal':
        return <PersonalDetailsStep />;
      case 'work':
        return <WorkDetailsStep />;
      default:
        return <UsernameStep />;
    }
  };

   if (!currentStep) return null;

  return (
    <div className="min-h-full bg-white flex flex-col">
      <div className="flex-1 flex flex-col justify-center py-6 px-4 sm:px-6 lg:px-8">
        <div className="mx-auto w-full max-w-md lg:shadow-lg lg:p-8 rounded-sm">
          {renderStep()}
        </div>
      </div>
    </div>
  );
}

export default FillUserDetailsPage;