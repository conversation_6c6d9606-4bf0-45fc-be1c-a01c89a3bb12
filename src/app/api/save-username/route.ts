import { NextRequest, NextResponse } from 'next/server';
import { auth } from '../../../../auth';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { username } = await request.json();

    if (!username) {
      return NextResponse.json(
        { error: 'Username is required' },
        { status: 400 }
      );
    }

    // Get user data from session
    const backendData = (session as any).user?.backendData;
    const token = backendData?.token;

    if (!token) {
      return NextResponse.json(
        { error: 'Missing authentication data' },
        { status: 401 }
      );
    }

    // Call backend API to save username
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_BASE_URL}/backend/api/v1/user/profile/username`,
      {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
          'x-api-key': process.env.NEXT_PUBLIC_API_KEY || '',
        },
        body: JSON.stringify({
          username,
        }),
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to save username');
    }

    const data = await response.json();
    
    return NextResponse.json({
      success: true,
      message: 'Username saved successfully',
      data,
    });
  } catch (error) {
    console.error('Error saving username:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Failed to save username',
        success: false 
      },
      { status: 500 }
    );
  }
}
