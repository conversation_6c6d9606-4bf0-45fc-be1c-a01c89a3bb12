import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // TODO: Replace with actual API call to your backend
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_BASE_URL}/backend/api/v1/master/country/options`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': process.env.NEXT_PUBLIC_API_KEY || '',
        },
      }
    );

    if (!response.ok) {
      throw new Error('Failed to fetch countries');
    }

    const data = await response.json();
    
    // Transform the data to match our Select component format
    const countries = data.data?.map((country: any) => ({
      value: country.iso2,
      label: country.name,
    })) || [];
    
    return NextResponse.json({
      success: true,
      countries,
    });
  } catch (error) {
    console.error('Error fetching countries:', error);
    
    // Fallback countries
    const fallbackCountries = [
      { value: 'US', label: 'United States' },
      { value: 'IN', label: 'India' },
      { value: 'GB', label: 'United Kingdom' },
      { value: 'CA', label: 'Canada' },
      { value: 'AU', label: 'Australia' },
      { value: 'DE', label: 'Germany' },
      { value: 'FR', label: 'France' },
      { value: 'JP', label: 'Japan' },
      { value: 'BR', label: 'Brazil' },
      { value: 'CN', label: 'China' },
    ];
    
    return NextResponse.json({
      success: true,
      countries: fallbackCountries,
    });
  }
}
