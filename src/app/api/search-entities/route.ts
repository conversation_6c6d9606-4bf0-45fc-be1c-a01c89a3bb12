import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { query } = await request.json();

    if (!query || query.length < 2) {
      return NextResponse.json({
        success: true,
        results: [],
      });
    }

    // TODO: Replace with actual API call to your backend
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_BASE_URL}/backend/api/v1/company/entity/all/options?search=${encodeURIComponent(query)}&page=1`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': process.env.NEXT_PUBLIC_API_KEY || '',
        },
      }
    );

    if (!response.ok) {
      throw new Error('Failed to search entities');
    }

    const data = await response.json();
    
    // Transform the data to match our SearchableSelect format
    const results = data.data?.map((item: any) => ({
      id: item.id,
      name: item.name,
      dataType: item.dataType || 'entity',
    })) || [];
    
    return NextResponse.json({
      success: true,
      results,
    });
  } catch (error) {
    console.error('Error searching entities:', error);
    
    // Fallback entities
    const fallbackEntities = [
      { id: '1', name: 'Google', dataType: 'organization' },
      { id: '2', name: 'Microsoft', dataType: 'organization' },
      { id: '3', name: 'Apple', dataType: 'organization' },
      { id: '4', name: 'Amazon', dataType: 'organization' },
      { id: '5', name: 'Meta', dataType: 'organization' },
      { id: '6', name: 'Netflix', dataType: 'organization' },
      { id: '7', name: 'Tesla', dataType: 'organization' },
      { id: '8', name: 'Freelancer', dataType: 'other' },
      { id: '9', name: 'Self Employed', dataType: 'other' },
    ].filter(item => 
      item.name.toLowerCase().includes(query.toLowerCase())
    );
    
    return NextResponse.json({
      success: true,
      results: fallbackEntities,
    });
  }
}
