import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { query } = await request.json();

    if (!query || query.length < 2) {
      return NextResponse.json({
        success: true,
        results: [],
      });
    }

    // TODO: Replace with actual API call to your backend
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_BASE_URL}/backend/api/v1/company/designation/options?search=${encodeURIComponent(query)}&page=1`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': process.env.NEXT_PUBLIC_API_KEY || '',
        },
      }
    );

    if (!response.ok) {
      throw new Error('Failed to search designations');
    }

    const data = await response.json();
    
    // Transform the data to match our SearchableSelect format
    const results = data.data?.map((item: any) => ({
      id: item.id,
      name: item.name,
      dataType: item.dataType || 'designation',
    })) || [];
    
    return NextResponse.json({
      success: true,
      results,
    });
  } catch (error) {
    console.error('Error searching designations:', error);
    
    // Fallback designations
    const fallbackDesignations = [
      { id: '1', name: 'Software Engineer', dataType: 'designation' },
      { id: '2', name: 'Product Manager', dataType: 'designation' },
      { id: '3', name: 'Data Scientist', dataType: 'designation' },
      { id: '4', name: 'DevOps Engineer', dataType: 'designation' },
      { id: '5', name: 'UI/UX Designer', dataType: 'designation' },
    ].filter(item => 
      item.name.toLowerCase().includes(request.url.includes('query') ? 
        new URL(request.url).searchParams.get('query')?.toLowerCase() || '' : '')
    );
    
    return NextResponse.json({
      success: true,
      results: fallbackDesignations,
    });
  }
}
