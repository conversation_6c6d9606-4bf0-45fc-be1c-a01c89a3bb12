import { NextRequest, NextResponse } from 'next/server';
import { auth } from '../../../../auth';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { username } = await request.json();

    if (!username) {
      return NextResponse.json(
        { error: 'Username is required' },
        { status: 400 }
      );
    }

    // Get user data from session
    const backendData = (session as any).user?.backendData;
    const token = backendData?.token;

    if (!token) {
      return NextResponse.json(
        { error: 'Missing authentication data' },
        { status: 401 }
      );
    }

    // Call backend API to check username availability
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_BASE_URL}/backend/api/v1/user/profile/username?username=${encodeURIComponent(username)}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
          'x-api-key': process.env.NEXT_PUBLIC_API_KEY || '',
        },
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to check username availability');
    }

    const data = await response.json();
    
    return NextResponse.json({
      available: !data.exists, // Assuming the API returns { exists: boolean }
      message: data.exists ? 'Username is already taken' : 'Username is available',
    });
  } catch (error) {
    console.error('Error checking username:', error);
    
    // Fallback: simulate some taken usernames for demo
    try {
      const { username } = await request.json();
      const takenUsernames = ['admin', 'user', 'test', 'demo', 'john', 'jane'];
      const available = !takenUsernames.includes(username?.toLowerCase() || '');
      
      return NextResponse.json({
        available,
        message: available ? 'Username is available' : 'Username is already taken',
      });
    } catch {
      return NextResponse.json(
        { error: 'Invalid request' },
        { status: 400 }
      );
    }
  }
}
