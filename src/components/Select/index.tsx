'use client';

import React, { useState, useRef, useEffect } from 'react';
import { ChevronDownIcon, CheckIcon } from '@heroicons/react/24/outline';

export type SelectOptionI = {
  value: string;
  label: string;
  disabled?: boolean;
};

export type SelectPropsI = {
  options: SelectOptionI[];
  value?: string;
  placeholder?: string;
  onValueChange?: (value: string) => void;
  disabled?: boolean;
  error?: string;
  className?: string;
  label?: string;
};

const Select = React.forwardRef<HTMLButtonElement, SelectPropsI>(
  ({ options, value, placeholder = 'Select an option', onValueChange, disabled, error, className = '', label }, ref) => {
    const [isOpen, setIsOpen] = useState(false);
    const [selectedValue, setSelectedValue] = useState(value || '');
    const dropdownRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
      setSelectedValue(value || '');
    }, [value]);

    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
          setIsOpen(false);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, []);

    const handleSelect = (optionValue: string) => {
      setSelectedValue(optionValue);
      setIsOpen(false);
      onValueChange?.(optionValue);
    };

    const selectedOption = options.find(option => option.value === selectedValue);

    return (
      <div className={`relative ${className}`}>
        {label && (
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {label}
          </label>
        )}
        <div className="relative" ref={dropdownRef}>
          <button
            ref={ref}
            type="button"
            className={`
              relative w-full cursor-default rounded-md border bg-white py-2 pl-3 pr-10 text-left shadow-sm 
              focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary sm:text-sm
              ${error ? 'border-red-300' : 'border-gray-300'}
              ${disabled ? 'bg-gray-50 text-gray-500 cursor-not-allowed' : 'hover:border-gray-400'}
            `}
            aria-haspopup="listbox"
            aria-expanded={isOpen}
            onClick={() => !disabled && setIsOpen(!isOpen)}
            disabled={disabled}
          >
            <span className="block truncate">
              {selectedOption ? selectedOption.label : placeholder}
            </span>
            <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
              <ChevronDownIcon
                className={`h-5 w-5 text-gray-400 transition-transform duration-200 ${
                  isOpen ? 'rotate-180' : ''
                }`}
                aria-hidden="true"
              />
            </span>
          </button>

          {isOpen && (
            <div className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
              {options.map((option) => (
                <div
                  key={option.value}
                  className={`
                    relative cursor-default select-none py-2 pl-3 pr-9 
                    ${option.disabled 
                      ? 'text-gray-400 cursor-not-allowed' 
                      : 'text-gray-900 cursor-pointer hover:bg-primary hover:text-white'
                    }
                    ${selectedValue === option.value ? 'bg-primary text-white' : ''}
                  `}
                  onClick={() => !option.disabled && handleSelect(option.value)}
                >
                  <span className={`block truncate ${selectedValue === option.value ? 'font-medium' : 'font-normal'}`}>
                    {option.label}
                  </span>
                  {selectedValue === option.value && (
                    <span className="absolute inset-y-0 right-0 flex items-center pr-4">
                      <CheckIcon className="h-5 w-5" aria-hidden="true" />
                    </span>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
        {error && (
          <p className="mt-1 text-sm text-red-600">{error}</p>
        )}
      </div>
    );
  }
);

Select.displayName = 'Select';

export { Select };
