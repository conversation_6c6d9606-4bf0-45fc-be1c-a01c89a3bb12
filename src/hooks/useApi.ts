'use client';

import { useCallback } from 'react';
import { isFilled, omit } from '@/utilities/data/object';
import { generateUrl } from '@/utilities/networks/api';
import APIResError from '@/errors/networks/APIResError';
import AppError from '@/errors/networks/AppError';
import { getOrGenerateDeviceId } from '@/utils/deviceId';
import { parseApiError } from '@/utils/errorParser';
import { showToast } from '@/utils/toast';
import useAuth from './useAuth';

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || '';

export type MethodI = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
export type HeadersI = Record<string, string>;

export type APICallI<PayloadT = unknown> = {
  isAuth?: boolean;
  payload?: PayloadT;
  query?: Record<string, unknown>;
  routeId?: string | number;
  headers?: HeadersI;
};

const useApi = () => {
  const { session } = useAuth();
  const getHeaders = useCallback(async (): Promise<HeadersI> => {
    const deviceId = getOrGenerateDeviceId();

    return {
      'x-api-key': process.env.NEXT_PUBLIC_API_KEY || '',
      Accept: 'application/json',
      'x-device-id': deviceId,
      'x-platform': 'web_app',
      'x-version-no': process.env.NEXT_PUBLIC_VERSION_NO || '3.0',
    };
  }, []);

  const apiCall = useCallback(
    async <PayloadT = unknown, ResponseT = unknown>(
      path: string,
      method: MethodI,
      { isAuth = true, payload, query, routeId, headers }: APICallI<PayloadT>
    ): Promise<ResponseT> => {
      try {
        const baseUrl = BASE_URL;

        const url = generateUrl({
          baseUrl,
          path,
          query,
          routeId,
        });

        console.log('🌐 API Call:', {
          method,
          url,
          isAuth,
          hasPayload: !!payload,
        });

        let baseHeaders = await getHeaders();

        if (isFilled(headers)) {
          baseHeaders = { ...baseHeaders, ...headers };
        }

        if (['POST', 'PATCH', 'PUT'].includes(method) && isFilled(payload)) {
          baseHeaders['Content-Type'] = 'application/json';
        }

        if (method === 'DELETE') {
          baseHeaders = omit(baseHeaders, [
            'Content-Type',
            'Accept',
          ]) as HeadersI;
        }

        if (isAuth) {
          const token = session ? session?.user.backendJwtToken : null;
          const tokenKey = 'jwtToken';

          if (token) {
            baseHeaders.Authorization = `Bearer ${token}`;
          } else {
            // Fallback to localStorage if session doesn't have token
            const fallbackToken =
              typeof window !== 'undefined'
                ? localStorage.getItem(tokenKey)
                : null;

            if (fallbackToken) {
              baseHeaders.Authorization = `Bearer ${fallbackToken}`;
              console.log(
                `🔐 Added Bearer token from localStorage fallback (${tokenKey})`
              );
            }
          }
        }

        const options: RequestInit = {
          headers: baseHeaders,
          method,
          credentials: 'include',
        };

        if (isFilled(payload)) {
          options.body = JSON.stringify(payload);
        }

        const response = await fetch(url, options);

        const contentType = response.headers.get('content-type');
        const isJson = contentType?.includes('application/json');
        const json = isJson ? await response.json() : null;

        if (response.status >= 200 && response.status < 300) {
          console.log('✅ API Call successful:', {
            status: response.status,
            url,
          });
          return json as ResponseT;
        } else if (response.status === 401) {
          console.error('🔒 Authentication failed - clearing tokens');

          // Clear auth state on unauthorized
          if (typeof window !== 'undefined') {
            localStorage.removeItem('token');
            localStorage.removeItem('jwtToken');
            localStorage.removeItem('userProfileId');
          }

          // Parse and show auth error
          showToast({
            type: 'error',
            message: 'Authentication Failed',
            description: 'Please login again to continue.',
          });
        } else {
          // Parse and show API error
          const parsedError = parseApiError(json);
          showToast({
            type: 'error',
            message: parsedError.message,
            description: parsedError.description,
          });
        }

        throw new APIResError(
          response.status,
          json || { message: 'No response body' }
        );
      } catch (error) {
        console.error('❌ API Call failed:', { method, path, error });

        if (error instanceof APIResError) {
          throw error;
        } else if (error instanceof TypeError) {
          // Network error
          showToast({
            type: 'error',
            message: 'Network Error',
            description: 'Please check your internet connection and try again.',
          });
          throw error;
        }

        // Unknown error
        const parsedError = parseApiError(error);
        showToast({
          type: 'error',
          message: parsedError.message,
          description: parsedError.description,
        });
        throw new AppError('Unknown error', error as Error);
      }
    },
    [session, getHeaders]
  );

  return {
    apiCall,
    // Utility to check if user is authenticated
    isAuthenticated: !!session,
    // Access to session data
    session,
  };
};

export default useApi;
